import {
	MainProcessAPI,
	MainProcessEvents,
	RendererProcessEvents,
	ElectronAPI,
	IsUpdateAvailableOptions,
	IsUpdateAvailableResponse,
	WindowFactoryOptions,
	DownloadTaskOptions,
	DownloadEvent
} from '../../../method.types'

import { EventEmitter } from 'events'
import { DownloadProgress } from '../../../core/downloader'
import { v4 as uuid } from 'uuid'

/**
 * ElectronClient 事件接口
 * 定义 ElectronClient 自身可以发出的事件
 */
export interface ElectronClientEvents {
	// 动态事件，用于下载任务等
	[key: string]: any
}

/**
 * Electron 客户端工具类
 * 提供更便捷的API调用和事件处理方法
 * 内置 EventEmitter 支持事件监听
 */
export class ElectronClient extends EventEmitter<ElectronClientEvents> {
	private api: ElectronAPI
	private eventUnsubscribers: Map<string, () => void> = new Map()

	constructor() {
		super()
		if (!window.electronAPI) {
			throw new Error('ElectronAPI is not available. Make sure the preload script is loaded.')
		}
		this.api = window.electronAPI
		this.downloadListener()
	}

	private downloadListener() {
		this.api.on('download-event', (downloadEvent: DownloadEvent) => {
			const eventName = `${downloadEvent.event}-${downloadEvent.taskId}`
			this.emit(eventName, downloadEvent.data)
		})
	}

	// ============================================================================
	// API 调用方法
	// ============================================================================

	/**
	 * 调用主进程方法
	 */
	private async invoke<K extends keyof MainProcessAPI>(
		method: K,
		...args: Parameters<MainProcessAPI[K]>
	): Promise<ReturnType<MainProcessAPI[K]>> {
		return this.api.invoke(method, ...args)
	}

	// ============================================================================
	// 便捷的API方法
	// ============================================================================

	/**
	 * 获取系统信息
	 */
	async getSystemInfo() {
		return this.invoke('getSystemInfo')
	}

	/**
	 * 获取应用版本
	 */
	async getAppVersion() {
		return this.invoke('getAppVersion')
	}

	/**
	 * 窗口控制
	 */
	async minimizeWindow() {
		return this.invoke('minimizeWindow')
	}

	async maximizeWindow() {
		return this.invoke('maximizeWindow')
	}

	async closeWindow() {
		return this.invoke('closeWindow')
	}

	async updateFinishedWindow() {
		this.api.emit('updater-open-main-window', { action: 'open' })
	}

	/**
	 * 应用设置
	 */
	async getSettings() {
		return this.invoke('getSettings')
	}

	async updateSettings(settings: Parameters<MainProcessAPI['updateSettings']>[0]) {
		return this.invoke('updateSettings', settings)
	}

	/**
	 * 配置管理
	 */
	async getAppPath() {
		return this.invoke('getAppPath')
	}

	async getPackagePath() {
		return this.invoke('getPackagePath')
	}

	async getAssetsPath() {
		return this.invoke('getAssetsPath')
	}

	async getWindowAdapter() {
		return this.invoke('getWindowAdapter')
	}

	/**
	 * 窗口工厂管理
	 */
	async openMainWindow(options: WindowFactoryOptions) {
		return this.invoke('openMainWindow', options)
	}

	async closeCustomWindow(windowId: string) {
		return this.invoke('closeCustomWindow', windowId)
	}

	async moveWindowToLeft(windowId?: string) {
		return this.invoke('moveWindowToLeft', windowId)
	}

	async moveWindowToRight(windowId?: string) {
		return this.invoke('moveWindowToRight', windowId)
	}

	async getActiveWindowId() {
		return this.invoke('getActiveWindowId')
	}

	/**
	 * 浏览器代理功能
	 */
	async getTRTCSdkPath() {
		return this.invoke('getTRTCSdkPath')
	}

	async getTRTCLogPath() {
		return this.invoke('getTRTCLogPath')
	}

	async openDevTools() {
		return this.invoke('openDevTools')
	}

	/**
	 * 包管理器功能
	 */
	async getLocalPackageVersion(pack: string) {
		return this.invoke('getLocalPackageVersion', pack)
	}

	async getServerPackageVersion(url: string) {
		return this.invoke('getServerPackageVersion', url)
	}

	async isUpdateAvailable(options: IsUpdateAvailableOptions): Promise<IsUpdateAvailableResponse> {
		return this.invoke('isUpdateAvailable', options)
	}

	async startDownloadTask(options: DownloadTaskOptions): Promise<DownloadTask> {
		if (!options.taskId) {
			options.taskId = uuid()
		}

		const downloadTask = new DownloadTask(options.taskId, this)
		await this.invoke('startDownloadTask', options)
		return Promise.resolve(downloadTask)
	}

	async abortDownloadTask(identity: string) {
		return this.invoke('abortDownloadTask', identity)
	}

	async decompressZip(options: Parameters<MainProcessAPI['decompressZip']>[0]) {
		return this.invoke('decompressZip', options)
	}

	// ============================================================================
	// 主进程事件处理方法（兼容现有的 MainProcessEvents）
	// ============================================================================

	/**
	 * 监听主进程事件
	 */
	onMainProcess<K extends keyof MainProcessEvents>(
		event: K,
		listener: (data: MainProcessEvents[K]) => void
	): () => void
	/**
	 * 监听动态事件（用于任务相关事件等）
	 */
	onMainProcess(event: string, listener: (data: any) => void): () => void
	onMainProcess<K extends keyof MainProcessEvents>(
		event: K | string,
		listener: (data: MainProcessEvents[K] | any) => void
	): () => void {
		const unsubscribe = this.api.on(event as any, listener)
		this.eventUnsubscribers.set(event, unsubscribe)
		return () => {
			unsubscribe()
			this.eventUnsubscribers.delete(event)
		}
	}

	/**
	 * 一次性监听主进程事件
	 */
	onceMainProcess<K extends keyof MainProcessEvents>(
		event: K,
		listener: (data: MainProcessEvents[K]) => void
	): void
	/**
	 * 一次性监听动态事件
	 */
	onceMainProcess(event: string, listener: (data: any) => void): void
	onceMainProcess<K extends keyof MainProcessEvents>(
		event: K | string,
		listener: (data: MainProcessEvents[K] | any) => void
	): void {
		this.api.once(event as any, listener)
	}

	/**
	 * 移除主进程事件监听器
	 */
	offMainProcess<K extends keyof MainProcessEvents>(
		event: K,
		listener?: (data: MainProcessEvents[K]) => void
	): void
	/**
	 * 移除动态事件监听器
	 */
	offMainProcess(event: string, listener?: (data: any) => void): void
	offMainProcess<K extends keyof MainProcessEvents>(
		event: K | string,
		listener?: (data: MainProcessEvents[K] | any) => void
	): void {
		this.api.off(event as any, listener)
	}

	/**
	 * 向主进程发送事件
	 */
	emitToMainProcess<K extends keyof RendererProcessEvents>(
		event: K,
		data: RendererProcessEvents[K]
	): void
	/**
	 * 发送动态事件（用于任务相关事件等）
	 */
	emitToMainProcess(event: string, data: any): void
	emitToMainProcess<K extends keyof RendererProcessEvents>(
		event: K | string,
		data: RendererProcessEvents[K] | any
	): void {
		this.api.emit(event as any, data)
	}

	// ============================================================================
	// 兼容性方法（保持向后兼容）
	// ============================================================================

	/**
	 * @deprecated 使用 onMainProcess 替代
	 * 监听事件
	 */
	onEvent<K extends keyof MainProcessEvents>(
		event: K,
		listener: (data: MainProcessEvents[K]) => void
	): () => void
	/**
	 * @deprecated 使用 onMainProcess 替代
	 * 监听动态事件（用于任务相关事件等）
	 */
	onEvent(event: string, listener: (data: any) => void): () => void
	onEvent<K extends keyof MainProcessEvents>(
		event: K | string,
		listener: (data: MainProcessEvents[K] | any) => void
	): () => void {
		return this.onMainProcess(event as any, listener)
	}

	/**
	 * @deprecated 使用 onceMainProcess 替代
	 * 一次性监听事件
	 */
	onceEvent<K extends keyof MainProcessEvents>(
		event: K,
		listener: (data: MainProcessEvents[K]) => void
	): void
	/**
	 * @deprecated 使用 onceMainProcess 替代
	 * 一次性监听动态事件
	 */
	onceEvent(event: string, listener: (data: any) => void): void
	onceEvent<K extends keyof MainProcessEvents>(
		event: K | string,
		listener: (data: MainProcessEvents[K] | any) => void
	): void {
		this.onceMainProcess(event as any, listener)
	}

	/**
	 * @deprecated 使用 offMainProcess 替代
	 * 移除事件监听器
	 */
	offEvent<K extends keyof MainProcessEvents>(
		event: K,
		listener?: (data: MainProcessEvents[K]) => void
	): void
	/**
	 * @deprecated 使用 offMainProcess 替代
	 * 移除动态事件监听器
	 */
	offEvent(event: string, listener?: (data: any) => void): void
	offEvent<K extends keyof MainProcessEvents>(
		event: K | string,
		listener?: (data: MainProcessEvents[K] | any) => void
	): void {
		this.offMainProcess(event as any, listener)
	}

	/**
	 * @deprecated 使用 emitToMainProcess 替代，或使用继承的 emit 方法发送本地事件
	 * 向主进程发送事件
	 */
	emitEvent<K extends keyof RendererProcessEvents>(
		event: K,
		data: RendererProcessEvents[K]
	): boolean
	/**
	 * @deprecated 使用 emitToMainProcess 替代，或使用继承的 emit 方法发送本地事件
	 * 发送动态事件（用于任务相关事件等）
	 */
	emitEvent(event: string, data: any): boolean
	emitEvent<K extends keyof RendererProcessEvents>(
		event: K | string,
		data: RendererProcessEvents[K] | any
	): boolean {
		// 为了保持向后兼容，这里仍然发送到主进程
		this.emitToMainProcess(event as any, data)
		// 同时也发送本地事件
		return super.emit(event as string, data)
	}

	// ============================================================================
	// EventEmitter 代理方法
	// ============================================================================

	/**
	 * 监听本地事件（ElectronClient 自身发出的事件）
	 */
	addEventListener<K extends keyof ElectronClientEvents>(
		event: K,
		listener: (
			...args: ElectronClientEvents[K] extends unknown[]
				? ElectronClientEvents[K]
				: [ElectronClientEvents[K]]
		) => void
	): this
	addEventListener(event: string, listener: (...args: any[]) => void): this
	addEventListener(event: string, listener: (...args: any[]) => void): this {
		super.on(event, listener)
		return this
	}

	/**
	 * 移除本地事件监听器
	 */
	removeEventListener<K extends keyof ElectronClientEvents>(
		event: K,
		listener: (
			...args: ElectronClientEvents[K] extends unknown[]
				? ElectronClientEvents[K]
				: [ElectronClientEvents[K]]
		) => void
	): this
	removeEventListener(event: string, listener: (...args: any[]) => void): this
	removeEventListener(event: string, listener: (...args: any[]) => void): this {
		super.off(event, listener)
		return this
	}

	/**
	 * 一次性监听本地事件
	 */
	onceLocal<K extends keyof ElectronClientEvents>(
		event: K,
		listener: (
			...args: ElectronClientEvents[K] extends unknown[]
				? ElectronClientEvents[K]
				: [ElectronClientEvents[K]]
		) => void
	): this
	onceLocal(event: string, listener: (...args: any[]) => void): this
	onceLocal(event: string, listener: (...args: any[]) => void): this {
		super.once(event, listener)
		return this
	}

	/**
	 * 发送本地事件
	 */
	emitLocal<K extends keyof ElectronClientEvents>(
		event: K,
		...args: ElectronClientEvents[K] extends unknown[]
			? ElectronClientEvents[K]
			: [ElectronClientEvents[K]]
	): boolean
	emitLocal(event: string, ...args: any[]): boolean
	emitLocal(event: string, ...args: any[]): boolean {
		return super.emit(event, ...args)
	}

	/**
	 * 获取事件监听器数量
	 */
	listenerCount(event: string): number {
		return super.listenerCount(event)
	}

	/**
	 * 获取所有事件名称
	 */
	eventNames(): string[] {
		return super.eventNames() as string[]
	}

	/**
	 * 监听系统主题变化
	 */
	onThemeChange(callback: (theme: MainProcessEvents['system-theme-changed']) => void) {
		return this.onMainProcess('system-theme-changed', callback)
	}

	/**
	 * 监听通知
	 */
	onNotification(callback: (notification: MainProcessEvents['notification']) => void) {
		return this.onMainProcess('notification', callback)
	}

	/**
	 * 发送用户操作事件
	 */
	emitUserAction(action: string, data?: any) {
		this.emitToMainProcess('user-action', {
			action,
			data,
			timestamp: Date.now()
		})
	}

	/**
	 * 发送页面加载事件
	 */
	emitPageLoaded(url: string, title: string, loadTime: number) {
		this.emitToMainProcess('page-loaded', {
			url,
			title,
			loadTime
		})
	}

	/**
	 * 发送错误事件
	 */
	emitError(message: string, stack?: string, code?: string) {
		this.emitToMainProcess('error-occurred', {
			message,
			stack,
			code,
			timestamp: Date.now()
		})
	}

	// ============================================================================
	// 工具方法
	// ============================================================================

	/**
	 * 清理所有事件监听器
	 */
	cleanup(): void {
		// 清理主进程事件监听器
		this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe())
		this.eventUnsubscribers.clear()

		// 清理本地事件监听器
		this.removeAllListeners()
	}
}
export interface DownloadTaskEvents {
	complete: []
	progress: [DownloadProgress]
	error: [Error]
}

export class DownloadTask extends EventEmitter<DownloadTaskEvents> {
	private readonly taskId: string
	private client: ElectronClient

	constructor(taskId: string, client: ElectronClient) {
		super()
		this.taskId = taskId
		this.client = client
		this.bindListeners()
	}

	private bindListeners(): void {
		this.client.on(`complete-${this.taskId}`, () => {
			this.emit('complete')
			setTimeout(() => {
				this.unbindListeners()
			}, 200)
		})
		this.client.on(`progress-${this.taskId}`, (progress: DownloadProgress) => {
			this.emit('progress', progress)
		})
		this.client.on(`error-${this.taskId}`, (error: Error) => {
			this.emit('error', error)
		})
	}

	private unbindListeners(): void {
		this.client.removeAllListeners(`complete-${this.taskId}`)
		this.client.removeAllListeners(`progress-${this.taskId}`)
		this.client.removeAllListeners(`error-${this.taskId}`)
	}

	async cancel(): Promise<void> {
		await this.client.abortDownloadTask(this.taskId)
	}
}

// 导出单例实例
export const electronClient = new ElectronClient()

// 导出类型，方便其他地方使用
export type {
	MainProcessAPI,
	MainProcessEvents,
	RendererProcessEvents
} from '../../../method.types'
